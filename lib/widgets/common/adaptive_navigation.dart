import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../../config/platform_adaptations.dart';
import '../../config/adaptive_icons.dart';
import '../../config/design_system.dart';
import '../../config/app_icons.dart';
import 'adaptive_components.dart';

/// Adaptive navigation utilities for platform-specific navigation patterns
class AdaptiveNavigation {
  // =====================================================
  // NAVIGATION METHODS
  // =====================================================

  /// Platform-appropriate navigation to a new page
  static Future<T?> push<T extends Object?>(
    BuildContext context,
    Widget page, {
    RouteSettings? settings,
    bool fullscreenDialog = false,
  }) {
    return Navigator.push<T>(
      context,
      PlatformAdaptations.createPageRoute<T>(
        page: page,
        settings: settings,
        fullscreenDialog: fullscreenDialog,
      ),
    );
  }

  /// Platform-appropriate modal navigation
  static Future<T?> pushModal<T extends Object?>(
    BuildContext context,
    Widget page, {
    RouteSettings? settings,
  }) {
    return Navigator.push<T>(
      context,
      PlatformAdaptations.createModalRoute<T>(
        page: page,
        settings: settings,
      ),
    );
  }

  /// Platform-appropriate replacement navigation
  static Future<T?> pushReplacement<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget page, {
    RouteSettings? settings,
    TO? result,
  }) {
    return Navigator.pushReplacement<T, TO>(
      context,
      PlatformAdaptations.createPageRoute<T>(
        page: page,
        settings: settings,
      ),
      result: result,
    );
  }

  /// Platform-appropriate back navigation with haptic feedback
  static void pop<T extends Object?>(BuildContext context, [T? result]) {
    if (PlatformAdaptations.shouldUseHapticFeedback) {
      HapticFeedback.lightImpact();
    }
    Navigator.pop<T>(context, result);
  }

  // =====================================================
  // ADAPTIVE APP BAR
  // =====================================================

  /// Creates platform-appropriate app bar
  static PreferredSizeWidget createAdaptiveAppBar({
    required BuildContext context,
    Widget? title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    Color? foregroundColor,
    double? elevation,
    bool centerTitle = false,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (PlatformAdaptations.isIOS) {
      return AppBar(
        title: title,
        actions: actions,
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        backgroundColor: backgroundColor ??
            theme.appBarTheme.backgroundColor ??
            colorScheme.surface,
        foregroundColor: foregroundColor ??
            theme.appBarTheme.foregroundColor ??
            colorScheme.onSurface,
        elevation: DesignSystem.elevationNone, // iOS style flat app bar
        centerTitle: true, // iOS centers titles
        toolbarHeight: PlatformAdaptations.adaptiveAppBarHeight,
        leadingWidth: 80, // iOS style wider leading area
      );
    }

    return AppBar(
      title: title,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor ??
          theme.appBarTheme.backgroundColor ??
          colorScheme.surface,
      foregroundColor: foregroundColor ??
          theme.appBarTheme.foregroundColor ??
          colorScheme.onSurface,
      elevation: DesignSystem.getAdjustedElevation(
        elevation ?? PlatformAdaptations.adaptiveElevation,
      ),
      centerTitle: centerTitle,
      toolbarHeight: PlatformAdaptations.adaptiveAppBarHeight,
    );
  }

  // =====================================================
  // ADAPTIVE BACK BUTTON
  // =====================================================

  /// Creates platform-appropriate back button
  static Widget createAdaptiveBackButton({
    required BuildContext context,
    VoidCallback? onPressed,
    Color? color,
  }) {
    return IconButton(
      icon: Icon(
        AdaptiveIcons.back,
        size: AppIcons.sizeM, // Use standard medium size for back buttons
      ),
      onPressed: onPressed ?? () => pop(context),
      color: color,
      tooltip: MaterialLocalizations.of(context).backButtonTooltip,
    );
  }

  // =====================================================
  // ADAPTIVE SCROLL BEHAVIOR
  // =====================================================

  /// Creates platform-appropriate scroll configuration
  static ScrollConfiguration createAdaptiveScrollConfiguration({
    required Widget child,
  }) {
    return ScrollConfiguration(
      behavior: AdaptiveScrollBehavior(),
      child: child,
    );
  }
}

/// Platform-appropriate scroll behavior
class AdaptiveScrollBehavior extends ScrollBehavior {
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return PlatformAdaptations.adaptiveScrollPhysics;
  }

  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    if (PlatformAdaptations.isIOS) {
      return CupertinoScrollbar(
        controller: details.controller,
        child: child,
      );
    }
    return Scrollbar(
      controller: details.controller,
      child: child,
    );
  }
}

// =====================================================
// ADAPTIVE PAGE WRAPPER
// =====================================================

/// Wrapper widget that provides platform-appropriate page structure
class AdaptivePage extends StatelessWidget {
  const AdaptivePage({
    super.key,
    this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.appBarElevation,
    this.centerTitle,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.drawer,
    this.endDrawer,
    required this.body,
    this.resizeToAvoidBottomInset,
    this.extendBody = false,
    this.extendBodyBehindAppBar = false,
  });

  final Widget? title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? appBarElevation;
  final bool? centerTitle;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final Widget? drawer;
  final Widget? endDrawer;
  final Widget body;
  final bool? resizeToAvoidBottomInset;
  final bool extendBody;
  final bool extendBodyBehindAppBar;

  @override
  Widget build(BuildContext context) {
    final adaptiveAppBar = title != null
        ? AdaptiveNavigation.createAdaptiveAppBar(
            context: context,
            title: title,
            actions: actions,
            leading: leading,
            automaticallyImplyLeading: automaticallyImplyLeading,
            backgroundColor: backgroundColor,
            foregroundColor: foregroundColor,
            elevation: appBarElevation,
            centerTitle: centerTitle ?? PlatformAdaptations.isIOS,
          )
        : null;

    return AdaptiveNavigation.createAdaptiveScrollConfiguration(
      child: Scaffold(
        appBar: adaptiveAppBar,
        body: body,
        floatingActionButton: floatingActionButton,
        bottomNavigationBar: bottomNavigationBar,
        drawer: drawer,
        endDrawer: endDrawer,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
        extendBody: extendBody,
        extendBodyBehindAppBar: extendBodyBehindAppBar,
      ),
    );
  }
}

// =====================================================
// ADAPTIVE NAVIGATION UTILITIES
// =====================================================

/// Utility class for common navigation patterns
class NavigationUtils {
  /// Shows platform-appropriate confirmation dialog before navigation
  static Future<bool> showExitConfirmation(
    BuildContext context, {
    String? title,
    String? content,
  }) async {
    return await AdaptiveDialogs.showConfirmation(
      context: context,
      title: title ?? 'Exit',
      content: content ?? 'Are you sure you want to exit?',
      confirmText: 'Exit',
      cancelText: 'Cancel',
      isDestructive: true,
    );
  }

  /// Handles platform-appropriate back button behavior
  static Future<bool> handleBackButton(
    BuildContext context, {
    bool requireConfirmation = false,
    String? confirmationTitle,
    String? confirmationContent,
  }) async {
    if (requireConfirmation) {
      return await showExitConfirmation(
        context,
        title: confirmationTitle,
        content: confirmationContent,
      );
    }
    return true;
  }
}
