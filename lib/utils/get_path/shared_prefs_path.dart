import 'dart:io';

import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

// Future<Directory> getAnxSharedPrefsDir() async {
//   switch(defaultTargetPlatform) {
//     case TargetPlatform.android:
//       // com.example.app/shared_prefs
//       final docPath = await getAnxDocumentsPath();
//       final sharedPrefsDirPath = '${docPath.split('/app_flutter')[0]}/shared_prefs';
//       return Directory(sharedPrefsDirPath);
//     case TargetPlatform.windows:
//       return Directory("${(await getApplicationSupportDirectory()).path}\\shared_preferences.json");
//     default:
//       throw Exception('Unsupported platform');
//   }
// }

String getSharedPrefsFileName() {
  switch (defaultTargetPlatform) {
    case TargetPlatform.android:
      return 'FlutterSharedPreferences.xml';
    case TargetPlatform.windows:
      return 'shared_preferences.json';
    case TargetPlatform.macOS:
    case TargetPlatform.iOS:
      return 'com.anxcye.anxReader.plist';
    default:
      throw Exception('Unsupported platform');
  }
}

Future<File> getAnxShredPrefsFile() async {
  switch (defaultTargetPlatform) {
    case TargetPlatform.android:
      final docPath = await getAnxDocumentsPath();
      final appFlutterIndex = docPath.indexOf('/app_flutter');
      final basePath = appFlutterIndex != -1
          ? docPath.substring(0, appFlutterIndex)
          : docPath;
      final sharedPrefsDirPath = path.join(basePath, 'shared_prefs');
      return File(path.join(sharedPrefsDirPath, getSharedPrefsFileName()));

    case TargetPlatform.windows:
      return File(
        path.join(
          (await getApplicationSupportDirectory()).path,
          getSharedPrefsFileName(),
        ),
      );
    case TargetPlatform.macOS:
      final documentsPath = await getAnxDocumentsPath();
      final documentsIndex = documentsPath.indexOf('Documents');
      final basePath = documentsIndex != -1
          ? documentsPath.substring(0, documentsIndex)
          : documentsPath;
      final preferencesPath = path.join(basePath, 'Library', 'Preferences');
      return File(path.join(preferencesPath, getSharedPrefsFileName()));
    case TargetPlatform.iOS:
      final documentsPath = (await getApplicationDocumentsDirectory()).path;
      final documentsIndex = documentsPath.indexOf('Documents');
      final basePath = documentsIndex != -1
          ? documentsPath.substring(0, documentsIndex)
          : documentsPath;
      final preferencesPath = path.join(basePath, 'Library', 'Preferences');
      return File(path.join(preferencesPath, getSharedPrefsFileName()));
    default:
      throw Exception('Unsupported platform');
  }
}
