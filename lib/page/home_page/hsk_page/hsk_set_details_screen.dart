import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_learn_screen.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_practice_screen.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_review_screen.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/widgets/decorations/mountain_painter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HskSetDetailsScreen extends ConsumerWidget {
  final HskCharacterSet characterSet;

  const HskSetDetailsScreen({
    super.key,
    required this.characterSet,
  });

  /// Theme-aware gradient decoration with WCAG AAA compliance
  /// Replaces hardcoded colors with Material Design 3 theme colors
  BoxDecoration _getBackgroundGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          colorScheme.primaryContainer,
          colorScheme.secondaryContainer,
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Calculate progress statistics
    final totalViews = characterSet.characters.fold<int>(
      0,
      (total, character) => total + character.viewCount,
    );

    final mastered = characterSet.characters.where((c) => c.isMastered).length;
    final progressPercentage = characterSet.characters.isEmpty
        ? 0
        : (mastered / characterSet.characters.length * 100).round();

    // Calculate individual mode progress statistics using available fields
    final learnProgress = characterSet.characters.isEmpty
        ? 0
        : (characterSet.characters.where((c) => c.learnCycleState > 0).length /
                characterSet.characters.length *
                100)
            .round();

    final practiceProgress = characterSet.characters.isEmpty
        ? 0
        : (characterSet.characters
                    .where((c) => c.practiceCorrectStreak > 0)
                    .length /
                characterSet.characters.length *
                100)
            .round();

    final reviewProgress = characterSet.characters.isEmpty
        ? 0
        : (characterSet.characters
                    .where((c) => c.lastPracticedCorrectly != null)
                    .length /
                characterSet.characters.length *
                100)
            .round();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Characters ${characterSet.startId} - ${characterSet.endId}',
        ),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
      ),
      body: Container(
        decoration: _getBackgroundGradient(context),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(
              DesignSystem.spaceL + 4,
            ), // 24.0 (preserves exact spacing)
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Statistics Display - Optimized for performance
                _StatisticsCard(
                  totalViews: totalViews,
                  totalViewTime: characterSet.totalViewTime,
                  learnProgress: learnProgress,
                  practiceProgress: practiceProgress,
                  reviewProgress: reviewProgress,
                  progressPercentage: progressPercentage,
                  formatTime: _formatTime,
                ),

                const SizedBox(
                  height: DesignSystem.spaceXL + 8,
                ), // 32.0 (preserves exact spacing)

                // Mode Buttons
                ModeButton(
                  title: 'Learn',
                  icon: Icons.school,
                  onPressed: () {
                    AdaptiveNavigation.push(
                      context,
                      HskLearnScreen(
                        characterSet: characterSet,
                      ),
                    );
                  },
                ),

                const SizedBox(
                  height: DesignSystem.spaceM,
                ), // 16.0 (preserves exact spacing)

                ModeButton(
                  title: 'Practice',
                  icon: Icons.quiz,
                  onPressed: () {
                    AdaptiveNavigation.push(
                      context,
                      HskPracticeScreen(
                        characterSet: characterSet,
                      ),
                    );
                  },
                ),

                const SizedBox(
                  height: DesignSystem.spaceM,
                ), // 16.0 (preserves exact spacing)

                ModeButton(
                  title: 'Review',
                  icon: Icons.history_edu,
                  onPressed: () {
                    AdaptiveNavigation.push(
                      context,
                      HskReviewScreen(
                        characterSet: characterSet,
                      ),
                    );
                  },
                ),

                const Spacer(),

                // Mountain silhouette decoration - CONSISTENT WITH ALL HSK MODES
                const SizedBox(
                  height: 100,
                  child: Stack(
                    children: [
                      // Mountain decoration
                      Positioned.fill(
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: MountainDecoration(height: 80),
                        ),
                      ),
                      // No overlay elements needed for Set Details Screen
                      // but maintaining consistent Stack structure for visual uniformity
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatTime(int seconds) {
    if (seconds < 60) {
      return '$seconds sec';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      return '$minutes min';
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return '$hours h $minutes min';
    }
  }
}

/// Optimized Mode Button - Uses const styling for better performance
class ModeButton extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onPressed;

  const ModeButton({
    super.key,
    required this.title,
    required this.icon,
    required this.onPressed,
  });

  // Theme-aware button style with WCAG AAA compliance
  ButtonStyle _getButtonStyle(BuildContext context) => ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHigh,
        foregroundColor:
            DesignSystem.getSettingsTextColor(context, isPrimary: true),
        padding:
            const EdgeInsets.symmetric(vertical: DesignSystem.spaceM), // 16.0
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusM - 4), // 12.0
        ),
      );

  // Static text style for better performance
  static const TextStyle _textStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
  );

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: _getButtonStyle(context),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon),
          const SizedBox(width: DesignSystem.spaceM - 4), // 12.0
          Text(title, style: _textStyle),
        ],
      ),
    );
  }
}

/// Optimized Statistics Card Widget - Reduces nesting and improves performance
class _StatisticsCard extends StatelessWidget {
  final int totalViews;
  final int totalViewTime;
  final int learnProgress;
  final int practiceProgress;
  final int reviewProgress;
  final int progressPercentage;
  final String Function(int) formatTime;

  const _StatisticsCard({
    required this.totalViews,
    required this.totalViewTime,
    required this.learnProgress,
    required this.practiceProgress,
    required this.reviewProgress,
    required this.progressPercentage,
    required this.formatTime,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Theme.of(context)
          .colorScheme
          .surfaceContainerHighest
          .withValues(alpha: 0.8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(DesignSystem.radiusM - 4), // 12.0
      ),
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceM), // 16.0
        child: Column(
          children: [
            _StatRow('Total character views:', '$totalViews'),
            const SizedBox(height: DesignSystem.spaceS),
            _StatRow('Total time:', formatTime(totalViewTime)),
            const SizedBox(height: DesignSystem.spaceS),
            _StatRow('Learn progress:', '$learnProgress%'),
            const SizedBox(height: DesignSystem.spaceS),
            _StatRow('Practice progress:', '$practiceProgress%'),
            const SizedBox(height: DesignSystem.spaceS),
            _StatRow('Review progress:', '$reviewProgress%'),
            const SizedBox(height: DesignSystem.spaceS),
            _StatRow('Total progress:', '$progressPercentage%'),
            const SizedBox(height: DesignSystem.spaceM),
            LinearProgressIndicator(
              value: progressPercentage / 100,
              backgroundColor: Theme.of(context)
                  .colorScheme
                  .surfaceContainerHigh
                  .withValues(alpha: 0.6),
              valueColor: AlwaysStoppedAnimation<Color>(
                progressPercentage == 100
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.secondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Optimized Statistics Row Widget - Reduces repetitive code
class _StatRow extends StatelessWidget {
  final String label;
  final String value;

  const _StatRow(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
