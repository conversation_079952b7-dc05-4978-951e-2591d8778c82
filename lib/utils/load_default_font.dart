import 'dart:io';

import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart' as path;

Future<void> loadDefaultFont() async {
  final notoSansSC =
      await rootBundle.load('assets/fonts/NotoSansSC-Regular.ttf');
  final fontDir = getFontDir();
  final fontFile = File(path.join(fontDir.path, 'NotoSansSC-Regular.ttf'));
  if (!fontFile.existsSync()) {
    fontFile.writeAsBytesSync(notoSansSC.buffer.asUint8List());
  }
}
