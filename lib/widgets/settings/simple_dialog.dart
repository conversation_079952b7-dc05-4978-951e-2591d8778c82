import 'package:flutter/material.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/config/design_system.dart';

Future<dynamic> showSimpleDialog(
  String title,
  Function saveToPrefs,
  List<Widget> children,
) {
  final context = navigatorKey.currentContext!;
  return showDialog(
    context: context,
    builder: (BuildContext context) {
      return SimpleDialog(
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color:
                    DesignSystem.getSettingsTextColor(context, isPrimary: true),
              ),
        ),
        children: children,
      );
    },
  );
}

Widget dialogOption(String name, String value, Function saveToPrefs) {
  final context = navigatorKey.currentContext!;
  return SimpleDialogOption(
    onPressed: () async {
      saveToPrefs(value);
      Navigator.pop(context);
    },
    child: Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceXS),
      child: Text(
        name,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
            ),
      ),
    ),
  );
}
