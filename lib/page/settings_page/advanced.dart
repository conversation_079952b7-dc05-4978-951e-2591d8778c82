import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/page/settings_page/subpage/log_page.dart';
import 'package:dasso_reader/widgets/settings/performance_dashboard.dart';
import 'package:dasso_reader/widgets/settings/settings_section.dart';
import 'package:dasso_reader/widgets/settings/settings_tile.dart';
import 'package:dasso_reader/widgets/settings/settings_title.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class AdvancedSetting extends StatefulWidget {
  const AdvancedSetting({super.key});

  @override
  State<AdvancedSetting> createState() => _AdvancedSettingState();
}

class _AdvancedSettingState extends State<AdvancedSetting> {
  @override
  Widget build(BuildContext context) {
    return settingsSections(
      sections: [
        // Performance Dashboard section (only in debug/profile mode)
        if (!kReleaseMode)
          SettingsSection(
            title: const Text('Performance'),
            tiles: [
              SettingsTile.navigation(
                leading: const Icon(Icons.speed),
                title: const Text('Performance Dashboard'),
                description:
                    const Text('View app performance metrics and monitoring'),
                onPressed: onPerformanceDashboardPressed,
              ),
            ],
          ),
        SettingsSection(
          title: Text(L10n.of(context).settings_advanced_log),
          tiles: [
            SettingsTile.switchTile(
              title:
                  Text(L10n.of(context).settings_advanced_clear_log_when_start),
              leading: const Icon(Icons.delete_forever_outlined),
              initialValue: Prefs().clearLogWhenStart,
              onToggle: (value) {
                Prefs().saveClearLogWhenStart(value);
                setState(() {});
              },
            ),
            SettingsTile.navigation(
              leading: const Icon(Icons.bug_report),
              title: Text(L10n.of(context).settings_advanced_log),
              onPressed: onLogPressed,
            ),
          ],
        ),
      ],
    );
  }
}

void onLogPressed(BuildContext context) {
  AdaptiveNavigation.push(
    context,
    const LogPage(),
  );
}

void onPerformanceDashboardPressed(BuildContext context) {
  // This should never be called in release mode since the option is hidden
  if (kReleaseMode) return;

  // Enhanced with semantic context for accessibility
  AdaptiveNavigation.push(
    context,
    SemanticHelpers.group(
      child: const PerformanceDashboard(),
      label: 'Performance Dashboard',
      hint: 'View detailed app performance metrics and monitoring data',
    ),
  );
}
