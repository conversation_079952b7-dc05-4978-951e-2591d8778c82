import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';

/// A floating menu widget for the add book options.
Widget showAddBookMenu({
  required BuildContext context,
  required VoidCallback onPasteText,
  required VoidCallback onImportFile,
}) {
  final colorScheme = Theme.of(context).colorScheme;
  final screenSize = ResponsiveSystem.getScreenSize(context);

  // Calculate appropriate sizes based on screen dimensions
  final double menuWidth =
      DesignSystem.isTablet(context) ? 500 : screenSize.width * 0.85;
  const double itemPadding = DesignSystem.spaceM;
  const double itemSpacing = DesignSystem.spaceM;

  return AlertDialog(
    backgroundColor: colorScheme.surface,
    surfaceTintColor: colorScheme.surfaceTint,
    elevation: DesignSystem.elevationL,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(DesignSystem.radiusCircle),
    ),
    title: Text(
      'Add Book',
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
      textAlign: TextAlign.center,
    ),
    content: SizedBox(
      width: menuWidth,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Calculate item width based on available space
          final double actualItemWidth =
              (constraints.maxWidth - itemSpacing) / 2;

          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildMenuOption(
                context: context,
                icon: Icons.edit_note_rounded,
                label: 'Paste Text',
                width: actualItemWidth,
                onTap: () {
                  Navigator.of(context).pop();
                  onPasteText();
                },
              ),
              const SizedBox(width: itemSpacing),
              _buildMenuOption(
                context: context,
                icon: Icons.file_download_outlined,
                label: 'Import File',
                width: actualItemWidth,
                onTap: () {
                  Navigator.of(context).pop();
                  onImportFile();
                },
              ),
            ],
          );
        },
      ),
    ),
    contentPadding: const EdgeInsets.all(itemPadding),
    // No actions to keep a clean design
  );
}

Widget _buildMenuOption({
  required BuildContext context,
  required IconData icon,
  required String label,
  required double width,
  required VoidCallback onTap,
}) {
  final colorScheme = Theme.of(context).colorScheme;

  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(DesignSystem.radiusL),
    child: Container(
      width: width,
      height: width,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(DesignSystem.radiusL),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: DesignSystem.spaceXXL + DesignSystem.spaceM,
            height: DesignSystem.spaceXXL + DesignSystem.spaceM,
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(DesignSystem.radiusL),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: colorScheme.primary.withValues(alpha: 0.2),
                width: 1.5,
              ),
            ),
            child: Icon(
              icon,
              size: DesignSystem.widgetIconSizeLarge,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: DesignSystem.spaceS),
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    ),
  );
}
