import 'package:flutter/material.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/design_system.dart';

AppBar settingsAppBar(String title, BuildContext context) {
  final theme = Theme.of(context);
  return AppBar(
    leading: Icon<PERSON><PERSON>on(
      icon: Icon(
        AdaptiveIcons.back,
        size: DesignSystem.getAdjustedIconSize(
          28.0,
        ), // Use adaptive icon size with manufacturer adjustments
      ),
      onPressed: () {
        Navigator.pop(context);
      },
    ),
    title: Text(title),
    backgroundColor: theme.appBarTheme.backgroundColor,
    foregroundColor: theme.appBarTheme.foregroundColor,
  );
}
