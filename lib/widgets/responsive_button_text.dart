import 'package:flutter/material.dart';
import 'package:dasso_reader/config/typography.dart';
import 'package:dasso_reader/config/design_system.dart';

/// A responsive text widget specifically designed for HSK button content.
///
/// This widget automatically adjusts text size and layout to prevent overflow
/// on different devices with varying font rendering characteristics.
class ResponsiveButtonText extends StatelessWidget {
  final String? pinyin;
  final String? english;
  final String? character;
  final bool showPinyin;
  final bool showEnglish;
  final double maxWidth;
  final double maxHeight;
  final double? characterFontSize;
  final double? pinyinFontSize;
  final double? englishFontSize;
  final int? maxEnglishLines;

  const ResponsiveButtonText({
    super.key,
    this.pinyin,
    this.english,
    this.character,
    this.showPinyin = true,
    this.showEnglish = true,
    required this.maxWidth,
    required this.maxHeight,
    this.characterFontSize,
    this.pinyinFontSize,
    this.englishFontSize,
    this.maxEnglishLines,
  });

  @override
  Widget build(BuildContext context) {
    // For single character display
    if (character != null) {
      // Use responsive Chinese character style with manufacturer adjustments
      final responsiveStyle = characterFontSize != null
          ? HskTypography.getResponsiveChineseStyle(
              context,
              text: character,
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
            ).copyWith(fontSize: characterFontSize)
          : HskTypography.getResponsiveChineseStyle(
              context,
              text: character,
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
            );

      return FittedBox(
        fit: BoxFit.contain,
        child: Text(
          character!,
          style: responsiveStyle,
          textAlign: TextAlign.center,
        ),
      );
    }

    // For pinyin/english display
    return LayoutBuilder(
      builder: (context, constraints) {
        // Build the column content first
        final columnChildren = <Widget>[];

        // Add pinyin with FittedBox for proper scaling
        if (showPinyin && pinyin != null && pinyin!.isNotEmpty) {
          // Use responsive pinyin style with manufacturer adjustments
          final responsivePinyinStyle = pinyinFontSize != null
              ? HskTypography.getResponsivePinyinStyle(
                  context,
                  text: pinyin,
                  color: DesignSystem.getSettingsTextColor(
                    context,
                    isPrimary: true,
                  ),
                ).copyWith(fontSize: pinyinFontSize)
              : HskTypography.getResponsivePinyinStyle(
                  context,
                  text: pinyin,
                  color: DesignSystem.getSettingsTextColor(
                    context,
                    isPrimary: true,
                  ),
                );

          columnChildren.add(
            SizedBox(
              width: constraints.maxWidth,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.center,
                child: Text(
                  pinyin!,
                  style: responsivePinyinStyle,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                ),
              ),
            ),
          );
        }

        // Add spacing if both pinyin and english are shown
        if (showPinyin &&
            showEnglish &&
            columnChildren.isNotEmpty &&
            english != null &&
            english!.isNotEmpty) {
          columnChildren.add(const SizedBox(height: DesignSystem.spaceTiny));
        }

        // Add english with proper text wrapping
        if (showEnglish && english != null && english!.isNotEmpty) {
          // Use responsive English style with manufacturer adjustments
          final responsiveEnglishStyle = englishFontSize != null
              ? HskTypography.getResponsiveEnglishStyle(
                  context,
                  text: english,
                  color: DesignSystem.getSettingsTextColor(
                    context,
                    isPrimary: false,
                  ),
                ).copyWith(fontSize: englishFontSize)
              : HskTypography.getResponsiveEnglishStyle(
                  context,
                  text: english,
                  color: DesignSystem.getSettingsTextColor(
                    context,
                    isPrimary: false,
                  ),
                );

          // Calculate available height for English text
          final pinyinHeight =
              showPinyin && pinyin != null ? constraints.maxHeight * 0.4 : 0;
          final englishHeight = constraints.maxHeight -
              pinyinHeight -
              (showPinyin ? DesignSystem.spaceTiny : 0);

          columnChildren.add(
            SizedBox(
              width: constraints.maxWidth,
              height: englishHeight > 0 ? englishHeight : null,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.center,
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: constraints.maxWidth,
                  ),
                  child: Text(
                    english!,
                    style: responsiveEnglishStyle,
                    textAlign: TextAlign.center,
                    maxLines: maxEnglishLines ?? 2,
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                ),
              ),
            ),
          );
        }

        if (columnChildren.isEmpty) {
          return const SizedBox.shrink();
        }

        // Return the properly constrained column
        return Container(
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          alignment: Alignment.center,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: columnChildren,
          ),
        );
      },
    );
  }
}
