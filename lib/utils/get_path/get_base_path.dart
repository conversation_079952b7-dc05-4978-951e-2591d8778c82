import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

String documentPath = '';

Future<String> getAnxDocumentsPath() async {
  final directory = await getApplicationDocumentsDirectory();
  switch (defaultTargetPlatform) {
    case TargetPlatform.android:
      return directory.path;
    case TargetPlatform.windows:
      // return '${directory.path}\\AnxReader';
      return (await getApplicationSupportDirectory()).path;
    case TargetPlatform.linux:
      final anxReaderPath = path.join(directory.path, 'AnxReader');
      return anxReaderPath;
    case TargetPlatform.macOS:
      return directory.path;
    case TargetPlatform.iOS:
      return (await getApplicationSupportDirectory()).path;
    default:
      throw Exception('Unsupported platform');
  }
}

Future<Directory> getAnxDocumentDir() async {
  return Directory(await getAnxDocumentsPath());
}

void initBasePath() async {
  Directory appDocDir = await getAnxDocumentDir();
  documentPath = appDocDir.path;
  debugPrint('documentPath: $documentPath');
  final fileDir = getFileDir();
  final coverDir = getCoverDir();
  final fontDir = getFontDir();
  if (!fileDir.existsSync()) {
    fileDir.createSync(recursive: true);
  }
  if (!coverDir.existsSync()) {
    coverDir.createSync(recursive: true);
  }
  if (!fontDir.existsSync()) {
    fontDir.createSync(recursive: true);
  }
}

String getBasePath(String filePath) {
  // the path that in database using "/"
  filePath.replaceAll('/', Platform.pathSeparator);
  return path.join(documentPath, filePath);
}

Directory getFontDir({String? basePath}) {
  basePath ??= documentPath;
  return Directory(path.join(basePath, 'font'));
}

Directory getCoverDir({String? basePath}) {
  basePath ??= documentPath;
  return Directory(path.join(basePath, 'cover'));
}

Directory getFileDir({String? basePath}) {
  basePath ??= documentPath;
  return Directory(path.join(basePath, 'file'));
}
