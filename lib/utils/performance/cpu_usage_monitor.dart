import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// CPU usage monitoring and optimization system
///
/// This system provides:
/// - Real-time CPU usage tracking with idle state monitoring
/// - Background task optimization with intelligent scheduling
/// - CPU hotspot detection and optimization recommendations
/// - Main isolate protection with automatic offloading detection
class CPUUsageMonitor {
  bool _isInitialized = false;
  bool _isMonitoring = false;
  CPUConfig _config = CPUConfig.defaultConfig();
  Timer? _monitoringTimer;

  // CPU tracking data
  double _currentCPUUsage = 0.0;
  double _peakCPUUsage = 0.0;
  final List<CPUSnapshot> _cpuHistory = [];
  final List<CPUHotspot> _hotspots = [];

  // Background task tracking
  final Map<String, BackgroundTaskInfo> _backgroundTasks = {};

  // Performance metrics
  bool _isIdleState = true;
  DateTime? _lastHighUsageTime;

  // Native implementation tracking
  bool _nativeImplementationWorking = false;
  bool _warningShown = false;

  /// Initialize the CPU usage monitor
  Future<void> initialize(CPUConfig config) async {
    if (_isInitialized) return;

    _config = config;
    _isInitialized = true;

    // Get initial CPU reading
    await _updateCPUUsage();

    if (kDebugMode) {
      AnxLog.info(
        '⚡ CPUUsageMonitor: Initialized with ${_config.highUsageThreshold}% threshold',
      );
    }
  }

  /// Start CPU usage monitoring
  void startMonitoring() {
    if (!_isInitialized || _isMonitoring) return;

    _isMonitoring = true;
    _setupPeriodicMonitoring();

    if (kDebugMode) {
      AnxLog.info('⚡ CPUUsageMonitor: Started monitoring');
    }
  }

  /// Stop CPU usage monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;

    if (kDebugMode) {
      AnxLog.info('⚡ CPUUsageMonitor: Stopped monitoring');
    }
  }

  /// Get current CPU metrics
  CPUMetrics getMetrics() {
    return CPUMetrics(
      currentUsage: _currentCPUUsage,
      averageUsage: _calculateAverageUsage(),
      peakUsage: _peakCPUUsage,
      cpuHistory: List.from(_cpuHistory),
      hotspots: List.from(_hotspots),
      backgroundTaskCount: _backgroundTasks.length,
      isIdleState: _isIdleState,
      performanceScore: _calculatePerformanceScore(),
    );
  }

  /// Track a background task
  void trackBackgroundTask(
    String taskId,
    String type, {
    Map<String, dynamic>? metadata,
  }) {
    final info = BackgroundTaskInfo(
      taskId: taskId,
      type: type,
      startTime: DateTime.now(),
      metadata: metadata ?? {},
    );

    _backgroundTasks[taskId] = info;

    if (kDebugMode && _config.enableTaskLogging) {
      AnxLog.info('⚡ Background Task Started: $type ($taskId)');
    }
  }

  /// Untrack a background task when completed
  void untrackBackgroundTask(String taskId) {
    final info = _backgroundTasks.remove(taskId);

    if (info != null && kDebugMode && _config.enableTaskLogging) {
      final duration = DateTime.now().difference(info.startTime);
      AnxLog.info(
        '⚡ Background Task Completed: ${info.type} (${info.taskId}) - ${duration.inMilliseconds}ms',
      );
    }
  }

  /// Track a CPU hotspot
  void trackHotspot(
    String methodName,
    Duration executionTime, {
    String? className,
  }) {
    if (executionTime.inMilliseconds < _config.hotspotThresholdMs) return;

    final hotspot = CPUHotspot(
      methodName: methodName,
      className: className,
      executionTime: executionTime,
      timestamp: DateTime.now(),
      severity: _getHotspotSeverity(executionTime),
    );

    _hotspots.add(hotspot);

    // Maintain hotspot history
    while (_hotspots.length > _config.maxHotspotHistory) {
      _hotspots.removeAt(0);
    }

    if (kDebugMode && _config.enableHotspotLogging) {
      AnxLog.warning(
        '⚡ CPU Hotspot: $methodName - ${executionTime.inMilliseconds}ms (${hotspot.severity.name})',
      );
    }
  }

  /// Get current CPU usage
  double get currentCPUUsage => _currentCPUUsage;

  /// Check if currently in idle state
  bool get isIdleState => _isIdleState;

  /// Setup periodic CPU monitoring
  void _setupPeriodicMonitoring() {
    _monitoringTimer = Timer.periodic(_config.monitoringInterval, (_) async {
      await _updateCPUUsage();
      _analyzeUsagePatterns();
      _maintainHistory();
    });
  }

  /// Update current CPU usage
  Future<void> _updateCPUUsage() async {
    try {
      // Get CPU info from platform
      final cpuInfo = await _getCPUInfo();
      final newUsage = cpuInfo['cpuUsage'] as double? ?? 0.0;

      // Update current usage
      _currentCPUUsage = newUsage;

      // Update peak usage
      if (_currentCPUUsage > _peakCPUUsage) {
        _peakCPUUsage = _currentCPUUsage;
      }

      // Create CPU snapshot
      final snapshot = CPUSnapshot(
        timestamp: DateTime.now(),
        usage: _currentCPUUsage,
        backgroundTaskCount: _backgroundTasks.length,
      );

      _cpuHistory.add(snapshot);
    } catch (error) {
      if (kDebugMode) {
        AnxLog.warning('⚡ Failed to update CPU usage: $error');
      }
    }
  }

  /// Get CPU information from platform with enhanced error handling
  Future<Map<String, dynamic>> _getCPUInfo() async {
    try {
      if (PlatformAdaptations.isAndroid) {
        // Use platform channel to get accurate CPU info with timeout
        const platform = MethodChannel('dasso_reader/cpu');
        final result = await platform
            .invokeMethod('getCPUInfo')
            .timeout(const Duration(seconds: 2));

        // Validate result structure
        if (result is Map && result.containsKey('cpuUsage')) {
          final cpuUsage = result['cpuUsage'];
          if (cpuUsage is num && cpuUsage >= 0 && cpuUsage <= 100) {
            // Success! Native implementation working
            if (kDebugMode && !_nativeImplementationWorking) {
              AnxLog.info('⚡ Native CPU monitoring now available');
              _nativeImplementationWorking = true;
            }
            return Map<String, dynamic>.from(result);
          } else {
            if (kDebugMode) {
              AnxLog.warning('⚡ Invalid CPU usage value: $cpuUsage');
            }
            return {'cpuUsage': _estimateCPUUsage()};
          }
        } else {
          if (kDebugMode) {
            AnxLog.warning(
              '⚡ Invalid platform channel response format: $result',
            );
          }
          return {'cpuUsage': _estimateCPUUsage()};
        }
      } else {
        // Fallback for other platforms (iOS, Desktop, Web)
        return {'cpuUsage': _estimateCPUUsage()};
      }
    } on TimeoutException {
      if (kDebugMode) {
        AnxLog.warning('⚡ Platform channel timeout - using estimation');
      }
      return {'cpuUsage': _estimateCPUUsage()};
    } on PlatformException catch (e) {
      if (kDebugMode) {
        AnxLog.warning('⚡ Platform channel error: ${e.code} - ${e.message}');
      }
      return {'cpuUsage': _estimateCPUUsage()};
    } catch (error) {
      // Only show warning once per session to avoid spam
      if (kDebugMode && !_warningShown) {
        AnxLog.warning(
          '⚡ CPU monitoring using estimation (native implementation not available)',
        );
        _warningShown = true;
      }
      // Fallback to estimation
      return {'cpuUsage': _estimateCPUUsage()};
    }
  }

  /// Estimate CPU usage (fallback method)
  double _estimateCPUUsage() {
    // Basic estimation based on background tasks and app state
    final baseUsage =
        _backgroundTasks.isEmpty ? 5.0 : 15.0; // Base app CPU usage
    final taskUsage =
        _backgroundTasks.length * 10.0; // Estimate per background task

    return (baseUsage + taskUsage).clamp(0.0, 100.0);
  }

  /// Analyze CPU usage patterns
  void _analyzeUsagePatterns() {
    final wasIdleState = _isIdleState;

    // Determine if in idle state
    _isIdleState = _currentCPUUsage <= _config.idleThreshold;

    // Check for high usage
    if (_currentCPUUsage >= _config.highUsageThreshold) {
      _lastHighUsageTime = DateTime.now();

      if (wasIdleState && kDebugMode) {
        AnxLog.info(
          '⚡ CPU usage increased: ${_currentCPUUsage.toStringAsFixed(1)}%',
        );
      }
    } else if (!wasIdleState && _isIdleState && kDebugMode) {
      AnxLog.info(
        '⚡ CPU usage normalized: ${_currentCPUUsage.toStringAsFixed(1)}%',
      );
    }

    // Check for sustained high usage
    _checkSustainedHighUsage();
  }

  /// Check for sustained high CPU usage
  void _checkSustainedHighUsage() {
    if (_lastHighUsageTime == null) return;

    final sustainedDuration = DateTime.now().difference(_lastHighUsageTime!);

    if (sustainedDuration >= _config.sustainedHighUsageThreshold) {
      if (kDebugMode) {
        AnxLog.warning(
          '⚡ Sustained high CPU usage detected: ${sustainedDuration.inSeconds}s',
        );
      }

      // Suggest optimization
      _suggestOptimization();
    }
  }

  /// Suggest CPU optimization
  void _suggestOptimization() {
    final suggestions = <String>[];

    if (_backgroundTasks.isNotEmpty) {
      suggestions.add('Consider reducing background task frequency');
      suggestions.add('Move heavy operations to isolates');
    }

    if (_hotspots.isNotEmpty) {
      final topHotspot = _hotspots.last;
      suggestions.add('Optimize method: ${topHotspot.methodName}');
    }

    suggestions.add('Profile app with DevTools CPU profiler');

    if (kDebugMode) {
      AnxLog.info('⚡ CPU Optimization Suggestions:');
      for (final suggestion in suggestions) {
        AnxLog.info('  - $suggestion');
      }
    }
  }

  /// Get hotspot severity
  HotspotSeverity _getHotspotSeverity(Duration executionTime) {
    final ms = executionTime.inMilliseconds;

    if (ms >= _config.hotspotThresholdMs * 5) {
      return HotspotSeverity.severe;
    }
    if (ms >= _config.hotspotThresholdMs * 2) {
      return HotspotSeverity.moderate;
    }
    return HotspotSeverity.mild;
  }

  /// Calculate average CPU usage
  double _calculateAverageUsage() {
    if (_cpuHistory.isEmpty) return _currentCPUUsage;

    final total =
        _cpuHistory.fold<double>(0.0, (sum, snapshot) => sum + snapshot.usage);
    return total / _cpuHistory.length;
  }

  /// Maintain CPU history within limits
  void _maintainHistory() {
    while (_cpuHistory.length > _config.maxHistorySize) {
      _cpuHistory.removeAt(0);
    }
  }

  /// Calculate performance score (0-100)
  double _calculatePerformanceScore() {
    final averageUsage = _calculateAverageUsage();
    final usageScore = (100 - averageUsage).clamp(0.0, 100.0);

    // Penalize for hotspots
    final hotspotPenalty = _hotspots.length * 2.0;

    // Penalize for sustained high usage
    final sustainedPenalty = _lastHighUsageTime != null &&
            DateTime.now().difference(_lastHighUsageTime!) >=
                _config.sustainedHighUsageThreshold
        ? 20.0
        : 0.0;

    return (usageScore - hotspotPenalty - sustainedPenalty).clamp(0.0, 100.0);
  }

  /// Dispose of resources
  void dispose() {
    stopMonitoring();
    _cpuHistory.clear();
    _hotspots.clear();
    _backgroundTasks.clear();
    _isInitialized = false;

    if (kDebugMode) {
      AnxLog.info('⚡ CPUUsageMonitor: Disposed');
    }
  }
}

/// CPU usage monitoring configuration
class CPUConfig {
  const CPUConfig({
    required this.idleThreshold,
    required this.highUsageThreshold,
    required this.hotspotThresholdMs,
    required this.sustainedHighUsageThreshold,
    required this.monitoringInterval,
    required this.maxHistorySize,
    required this.maxHotspotHistory,
    required this.enableTaskLogging,
    required this.enableHotspotLogging,
  });

  final double idleThreshold; // CPU usage percentage for idle state
  final double highUsageThreshold; // CPU usage percentage for high usage
  final int hotspotThresholdMs; // Method execution time threshold in ms
  final Duration
      sustainedHighUsageThreshold; // Duration for sustained high usage
  final Duration monitoringInterval;
  final int maxHistorySize;
  final int maxHotspotHistory;
  final bool enableTaskLogging;
  final bool enableHotspotLogging;

  factory CPUConfig.defaultConfig() {
    return const CPUConfig(
      idleThreshold: 10.0, // 10% CPU usage for idle
      highUsageThreshold: 70.0, // 70% CPU usage for high usage
      hotspotThresholdMs: 50, // 50ms execution time threshold
      sustainedHighUsageThreshold:
          Duration(seconds: 10), // 10 seconds sustained
      monitoringInterval: Duration(seconds: 2),
      maxHistorySize: 100,
      maxHotspotHistory: 50,
      enableTaskLogging: kDebugMode,
      enableHotspotLogging: kDebugMode,
    );
  }
}

/// CPU metrics data
class CPUMetrics {
  const CPUMetrics({
    required this.currentUsage,
    required this.averageUsage,
    required this.peakUsage,
    required this.cpuHistory,
    required this.hotspots,
    required this.backgroundTaskCount,
    required this.isIdleState,
    required this.performanceScore,
  });

  final double currentUsage;
  final double averageUsage;
  final double peakUsage;
  final List<CPUSnapshot> cpuHistory;
  final List<CPUHotspot> hotspots;
  final int backgroundTaskCount;
  final bool isIdleState;
  final double performanceScore;

  /// Check if CPU performance is acceptable
  bool get isCPUPerformanceAcceptable => performanceScore >= 70.0;

  /// Get the most severe hotspot
  CPUHotspot? get mostSevereHotspot {
    if (hotspots.isEmpty) return null;

    return hotspots.fold<CPUHotspot?>(null, (most, current) {
      if (most == null || current.severity.index > most.severity.index) {
        return current;
      }
      return most;
    });
  }
}

/// CPU snapshot data
class CPUSnapshot {
  const CPUSnapshot({
    required this.timestamp,
    required this.usage,
    required this.backgroundTaskCount,
  });

  final DateTime timestamp;
  final double usage;
  final int backgroundTaskCount;
}

/// CPU hotspot data
class CPUHotspot {
  const CPUHotspot({
    required this.methodName,
    required this.executionTime,
    required this.timestamp,
    required this.severity,
    this.className,
  });

  final String methodName;
  final String? className;
  final Duration executionTime;
  final DateTime timestamp;
  final HotspotSeverity severity;

  /// Get full method identifier
  String get fullMethodName =>
      className != null ? '$className.$methodName' : methodName;
}

/// Background task tracking information
class BackgroundTaskInfo {
  const BackgroundTaskInfo({
    required this.taskId,
    required this.type,
    required this.startTime,
    required this.metadata,
  });

  final String taskId;
  final String type;
  final DateTime startTime;
  final Map<String, dynamic> metadata;

  /// Get task duration
  Duration get duration => DateTime.now().difference(startTime);
}

/// CPU hotspot severity levels
enum HotspotSeverity {
  mild,
  moderate,
  severe,
}
